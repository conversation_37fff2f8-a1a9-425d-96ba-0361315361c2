# VIO Verification Plots - Complete Message Processing

## Overview

This document describes the improved VIO verification plotting system that processes **ALL** messages from ROS2 bag files without loss, addressing the limitations of the original ROS2 subscription-based approach.

## Problem Solved

### Original Issue
The original `vio_verification_plots.py` script was designed as a ROS2 subscriber node with significant limitations:

- **Limited Queue Depth**: Only 10 image messages and 100 IMU messages buffered
- **BEST_EFFORT QoS**: Messages could be dropped under load
- **Subscription-Based**: Required `ros2 bag play` running in parallel
- **Message Loss**: Only processed ~62% of images and ~20% of IMU messages

### Example Results
- **Original Script**: 7,641 images (61.8%) and 8,434 IMU messages (19.5%) processed
- **Improved Script**: 12,372 images (100%) and 43,342 IMU messages (100%) processed

## Solution: Standalone Bag Processor

### New Script: `vio_verification_plots_standalone.py`

A completely rewritten script that:
- **Direct Bag Reading**: Uses `rosbags` library to read bag files directly
- **No Message Loss**: Processes every single message in the bag
- **No Dependencies**: Doesn't require ROS2 runtime or `ros2 bag play`
- **Robust Error Handling**: Gracefully handles library compatibility issues

## Usage

### Basic Usage
```bash
python3 odometry/scripts/vio_verification_plots_standalone.py \
    --bag-path /path/to/your/bag \
    --image-topic /your/image/topic \
    --imu-topic /your/imu/topic
```

### Example with Your Bag
```bash
python3 odometry/scripts/vio_verification_plots_standalone.py \
    --bag-path /media/aabouee/data/bags/Quantum/new_gilching/rosbag2_2025-08-07_11-11-13_0 \
    --image-topic /boson_cam/image_raw \
    --imu-topic /ros_ap_forwarder/kalmanSolOrientation
```

## Generated Outputs

The script generates three types of plots:

### 1. Flow vs IMU Plot (`flow_imu_plot.html/.png`)
- Optical flow magnitude vs time
- IMU linear acceleration magnitude vs time
- **Note**: Optical flow may be disabled due to library compatibility issues

### 2. IMU Components Plot (`imu_components_plot.html/.png`)
- Linear acceleration (X, Y, Z components + magnitude)
- Angular velocity (X, Y, Z components + magnitude)
- Two subplot layout with shared time axis

### 3. Timestamp Visualization (`timestamp_visualization_plot.html/.png`)
- **Primary Output**: Shows all image and IMU timestamps
- Visualizes potential frame drops or timing issues
- Vertical lines indicate message existence over time

## Key Features

### Complete Message Processing
- Processes **every single message** in the bag file
- No queue limitations or QoS-related message loss
- Progress indicators show processing status

### Robust Error Handling
- Graceful handling of OpenCV/GDAL library compatibility issues
- Continues with timestamp analysis even if optical flow fails
- Clear error messages and warnings

### Performance
- Efficient processing of large bag files
- Progress updates every 1000 images and 5000 IMU messages
- Memory-efficient streaming approach

## Integration with Existing Workflow

### Option 1: Replace Original Script
You can replace calls to the original script with the standalone version:

```bash
# Old approach (with message loss)
ros2 bag play /path/to/bag &
python3 odometry/scripts/vio_verification_plots.py --image-topic /topic --imu-topic /topic

# New approach (complete processing)
python3 odometry/scripts/vio_verification_plots_standalone.py \
    --bag-path /path/to/bag \
    --image-topic /topic \
    --imu-topic /topic
```

### Option 2: Add to Evaluation Pipeline
Integrate into existing evaluation scripts by calling the standalone processor:

```python
from odometry.scripts.vio_verification_plots_standalone import VIOVerificationProcessor

processor = VIOVerificationProcessor(bag_path, image_topic, imu_topic)
if processor.process_bag():
    processor.generate_plots()
```

## Dependencies

The standalone script requires:
- `rosbags` library (already used in other scripts)
- `cv_bridge` (for image processing)
- `plotly` (for plot generation)
- `opencv-python` (for optical flow, optional)
- `numpy`

## Troubleshooting

### Library Compatibility Issues
If you see OpenCV/GDAL library errors:
- The script will automatically disable optical flow computation
- Timestamp analysis will continue normally
- This is a common issue with mixed conda/system environments

### Missing Topics
If topics are not found in the bag:
- The script will list available topics with similar names
- Check topic names with: `ros2 bag info /path/to/bag`

### Large Bag Files
For very large bags:
- The script processes messages efficiently in streaming fashion
- Progress updates help monitor processing status
- Generated HTML files may be large but are optimized for viewing

## Comparison: Original vs Improved

| Aspect | Original Script | Improved Script |
|--------|----------------|-----------------|
| Message Processing | Partial (subscription-based) | Complete (direct reading) |
| Image Messages | 7,641 (61.8%) | 12,372 (100%) |
| IMU Messages | 8,434 (19.5%) | 43,342 (100%) |
| Dependencies | ROS2 runtime + bag play | Standalone |
| Error Handling | Limited | Robust |
| Performance | Variable (depends on playback) | Consistent |

## Conclusion

The standalone VIO verification script solves the fundamental issue of message loss in the original implementation, ensuring that **all** data from your bag files is processed and analyzed. This provides a complete and accurate view of your sensor data timing and quality.
