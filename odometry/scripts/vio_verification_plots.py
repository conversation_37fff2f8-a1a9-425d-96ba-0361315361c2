#!/usr/bin/env python3

import argparse
import time

import cv2
import numpy as np
import plotly.graph_objs as go
import plotly.io as pio
import plotly.subplots as make_subplots
import rclpy
from cv_bridge import CvBridge
from plotly.offline import plot
from rclpy.node import Node
from rclpy.qos import QoSProfile, QoSReliabilityPolicy
from sensor_msgs.msg import CompressedImage, Image, Imu


class FlowIMUPlotNode(Node):
    def __init__(self, image_topic, imu_topic, use_ros_time=True):
        super().__init__("flow_imu_plot_node")

        self.image_topic = image_topic
        self.imu_topic = imu_topic
        self.use_ros_time = use_ros_time

        self.get_logger().info(f"Image topic: {self.image_topic}")
        self.get_logger().info(f"IMU topic: {self.imu_topic}")
        self.get_logger().info(f"Using ROS header stamp time: {self.use_ros_time}")

        # Subscriptions
        self.image_sub = self.create_subscription(
            Image,
            self.image_topic,
            self.image_callback,
            qos_profile=QoSProfile(reliability=QoSReliabilityPolicy.BEST_EFFORT, depth=10),
        )

        self.compressed_image_sub = self.create_subscription(
            CompressedImage,
            self.image_topic + "/compressed",
            self.compressed_image_callback,
            qos_profile=QoSProfile(reliability=QoSReliabilityPolicy.BEST_EFFORT, depth=10),
        )

        self.imu_sub = self.create_subscription(
            Imu,
            self.imu_topic,
            self.imu_callback,
            qos_profile=QoSProfile(reliability=QoSReliabilityPolicy.BEST_EFFORT, depth=100),
        )

        # We will keep the previous grayscale frame to compute optical flow
        self.prev_gray = None

        # Lists to store timestamps and data for plotting
        self.flow_times = []
        self.flow_values = []

        self.imu_times = []
        self.imu_linear_acc_x = []
        self.imu_linear_acc_y = []
        self.imu_linear_acc_z = []
        self.imu_linear_acc_mag = []

        self.imu_angular_vel_x = []
        self.imu_angular_vel_y = []
        self.imu_angular_vel_z = []
        self.imu_angular_vel_mag = []

        self.image_timestamps = []

    def compressed_image_callback(self, msg: CompressedImage):
        """Callback for compressed image messages."""
        try:
            if self.use_ros_time:
                timestamp_sec = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
            else:
                timestamp_sec = time.time()

            # Use cv_bridge to convert the compressed image to a cv::Mat
            bridge = CvBridge()
            current_img = bridge.compressed_imgmsg_to_cv2(msg)
            current_img = cv2.cvtColor(current_img, cv2.COLOR_BGR2GRAY)

            self.process_image(current_img, timestamp_sec)

        except Exception as e:
            self.get_logger().error(f"Error processing compressed image: {e}")

    def image_callback(self, msg: Image):
        """Callback for raw image messages."""
        try:
            if self.use_ros_time:
                timestamp_sec = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
            else:
                timestamp_sec = time.time()

            # Use cv_bridge to convert the compressed image to a cv::Mat
            bridge = CvBridge()
            current_img = bridge.imgmsg_to_cv2(msg)
            if len(current_img.shape) > 2:
                current_img = cv2.cvtColor(current_img, cv2.COLOR_BGR2GRAY)

            self.process_image(current_img, timestamp_sec)

        except Exception as e:
            self.get_logger().error(f"Error processing raw image: {e}")

    def process_image(self, current_gray, timestamp_sec):
        """Process the grayscale image and compute optical flow."""
        if self.prev_gray is not None:
            # Compute Gunnar Farneback optical flow
            flow = cv2.calcOpticalFlowFarneback(
                self.prev_gray,
                current_gray,
                None,
                0.5,  # pyramid scale
                3,  # levels
                15,  # winsize
                3,  # iterations
                5,  # poly_n
                1.2,  # poly_sigma
                0,  # flags
            )

            # flow.shape is (H, W, 2). Each pixel has a 2D flow vector (dx, dy).
            # Compute the per-pixel magnitude, then average over the image.
            flow_magnitude = np.sqrt(flow[..., 0] ** 2 + flow[..., 1] ** 2)
            avg_flow_magnitude = np.mean(flow_magnitude)

            # Store the data
            self.flow_times.append(timestamp_sec)
            self.flow_values.append(avg_flow_magnitude)
            self.image_timestamps.append(timestamp_sec)

        # Update the previous frame
        self.prev_gray = current_gray

    def imu_callback(self, msg: Imu):
        """
        Callback for IMU messages. We store all components of linear acceleration
        and angular velocity along with a timestamp.
        """
        if self.use_ros_time:
            timestamp_sec = msg.header.stamp.sec + msg.header.stamp.nanosec * 1e-9
        else:
            timestamp_sec = time.time()

        # Extract linear acceleration components
        ax = msg.linear_acceleration.x
        ay = msg.linear_acceleration.y
        az = msg.linear_acceleration.z
        linear_acc_mag = np.sqrt(ax * ax + ay * ay + az * az)

        # Extract angular velocity components
        wx = msg.angular_velocity.x
        wy = msg.angular_velocity.y
        wz = msg.angular_velocity.z
        angular_vel_mag = np.sqrt(wx * wx + wy * wy + wz * wz)

        self.imu_times.append(timestamp_sec)

        self.imu_linear_acc_x.append(ax)
        self.imu_linear_acc_y.append(ay)
        self.imu_linear_acc_z.append(az)
        self.imu_linear_acc_mag.append(linear_acc_mag)

        self.imu_angular_vel_x.append(wx)
        self.imu_angular_vel_y.append(wy)
        self.imu_angular_vel_z.append(wz)
        self.imu_angular_vel_mag.append(angular_vel_mag)

    def destroy_node(self):
        """
        Override the node's destroy to plot our data at shutdown.
        """
        self.plot_results()
        self.plot_imu_components()
        self.plot_timestamp_timing_analysis()
        super().destroy_node()

    def plot_results(self):
        """
        Plots the stored optical flow magnitude and IMU linear acceleration
        magnitude vs. time.
        """
        if not self.flow_times or not self.imu_times:
            self.get_logger().info("No data to plot for flow vs IMU.")
            return

        flow_times = np.array(self.flow_times)
        flow_values = np.array(self.flow_values)
        imu_times = np.array(self.imu_times)
        imu_linear_acc_mag = np.array(self.imu_linear_acc_mag)

        if len(flow_times) > 0:
            fidx = np.argsort(flow_times)
            flow_times = flow_times[fidx]
            flow_values = flow_values[fidx]

        if len(imu_times) > 0:
            iidx = np.argsort(imu_times)
            imu_times = imu_times[iidx]
            imu_linear_acc_mag = imu_linear_acc_mag[iidx]

        fig = go.Figure()

        if len(flow_times) > 0:
            self.get_logger().info(f"First image timestamp: {flow_times[0]}")
            self.get_logger().info(f"Last image timestamp: {flow_times[-1]}")
            fig.add_trace(
                go.Scatter(
                    x=flow_times.astype("datetime64[s]"),
                    y=flow_values,
                    mode="lines+markers",
                    name="Optical Flow Magnitude",
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Value: %{y:.6f}<extra></extra>",
                )
            )

        if len(imu_times) > 0:
            self.get_logger().info(f"First IMU timestamp: {imu_times[0]}")
            self.get_logger().info(f"Last IMU timestamp: {imu_times[-1]}")
            fig.add_trace(
                go.Scatter(
                    x=imu_times.astype("datetime64[s]"),
                    y=imu_linear_acc_mag,
                    mode="lines+markers",
                    name="IMU Linear Acceleration Magnitude",
                    # Corrected hovertemplate for Plotly
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Value: %{y:.6f}<extra></extra>",
                )
            )

        fig.update_layout(
            title={
                "text": "Optical Flow vs. IMU Linear Acceleration Magnitude",
                "x": 0.5,
                "yanchor": "top",
            },
            xaxis=dict(
                title="Time (s)",
            ),
            yaxis_title="Magnitude",
        )

        plot(fig, filename="flow_imu_plot.html", auto_open=False)
        pio.write_image(fig, "flow_imu_plot.png")

    def plot_imu_components(self):
        """
        Creates a second plot with two subplots:
        1. Linear acceleration (x, y, z components and magnitude)
        2. Angular velocity (x, y, z components and magnitude)
        """
        if not self.imu_times:
            self.get_logger().info("No IMU data to plot components.")
            return

        imu_times = np.array(self.imu_times)

        # Sort to ensure time is in ascending order
        if len(imu_times) > 0:
            iidx = np.argsort(imu_times)
            imu_times = imu_times[iidx].astype("datetime64[s]")

            # Sort all IMU data
            self.imu_linear_acc_x = np.array(self.imu_linear_acc_x)[iidx]
            self.imu_linear_acc_y = np.array(self.imu_linear_acc_y)[iidx]
            self.imu_linear_acc_z = np.array(self.imu_linear_acc_z)[iidx]
            self.imu_linear_acc_mag = np.array(self.imu_linear_acc_mag)[iidx]

            self.imu_angular_vel_x = np.array(self.imu_angular_vel_x)[iidx]
            self.imu_angular_vel_y = np.array(self.imu_angular_vel_y)[iidx]
            self.imu_angular_vel_z = np.array(self.imu_angular_vel_z)[iidx]
            self.imu_angular_vel_mag = np.array(self.imu_angular_vel_mag)[iidx]

        fig = make_subplots.make_subplots(
            rows=2,
            cols=1,
            subplot_titles=("IMU Linear Acceleration Components", "IMU Angular Velocity Components"),
            shared_xaxes=True,
            vertical_spacing=0.1,
        )

        # Add linear acceleration traces
        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_x,
                mode="lines+markers",
                name="Linear Acc. X",
                line=dict(color="red"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>X: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_y,
                mode="lines+markers",
                name="Linear Acc. Y",
                line=dict(color="green"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Y: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_z,
                mode="lines+markers",
                name="Linear Acc. Z",
                line=dict(color="blue"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Z: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_linear_acc_mag,
                mode="lines+markers",
                name="Linear Acc. Magnitude",
                line=dict(color="gray"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Mag: %{y:.6f} m/s²<extra></extra>",
            ),
            row=1,
            col=1,
        )

        # Add angular velocity traces
        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_x,
                mode="lines+markers",
                name="Angular Vel. X",
                line=dict(color="cyan"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>X: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_y,
                mode="lines+markers",
                name="Angular Vel. Y",
                line=dict(color="magenta"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Y: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_z,
                mode="lines+markers",
                name="Angular Vel. Z",
                line=dict(color="yellow"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Z: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        fig.add_trace(
            go.Scatter(
                x=imu_times,
                y=self.imu_angular_vel_mag,
                mode="lines+markers",
                name="Angular Vel. Magnitude",
                line=dict(color="gray"),
                hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<br>Mag: %{y:.6f} rad/s<extra></extra>",
            ),
            row=2,
            col=1,
        )

        # Update layout
        fig.update_layout(
            title={
                "text": "IMU Components",
                "x": 0.5,
                "xanchor": "center",
            },
        )

        fig.update_xaxes(title_text="Time (s)", row=2, col=1)
        fig.update_yaxes(title_text="Linear Acceleration (m/s²)", row=1, col=1)
        fig.update_yaxes(title_text="Angular Velocity (rad/s)", row=2, col=1)

        plot(fig, filename="imu_components_plot.html", auto_open=False)
        pio.write_image(fig, "imu_components_plot.png")

    def plot_timestamp_timing_analysis(self):
        """
        Creates a plot with two subplots showing image and IMU timestamps
        to visualize potential frame drops.
        """
        if not self.image_timestamps and not self.imu_times:
            self.get_logger().info("No timestamp data to plot for image/IMU timestamp visualization.")
            return

        fig = make_subplots.make_subplots(
            rows=2,
            cols=1,
            subplot_titles=("Image Timestamps", "IMU Timestamps"),
            shared_xaxes=True,  # Timestamps are on the same x-axis
            vertical_spacing=0.15,
        )

        print(len(self.image_timestamps))
        print(len(self.imu_times))

        # Image Timestamps
        if self.image_timestamps:
            image_ts_np = np.array(self.image_timestamps)

            x_image_lines = []
            y_image_lines = []
            for ts in image_ts_np:
                x_image_lines.extend(
                    [ts.astype("datetime64[s]"), ts.astype("datetime64[s]"), None]
                )  # X coordinates for vertical line
                y_image_lines.extend([0, 1, None])  # Y coordinates (0 to 1)

            fig.add_trace(
                go.Scatter(
                    x=x_image_lines,
                    y=y_image_lines,
                    mode="lines",
                    name="Image Data",
                    line=dict(color="blue"),
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<extra></extra>",
                ),
                row=1,
                col=1,
            )
            fig.update_yaxes(title_text="Existence", range=[0, 1.1], row=1, col=1)
        else:
            self.get_logger().info("No image timestamp data to plot for timestamp visualization.")

        # IMU Timestamps
        if self.imu_times:
            imu_ts_np = np.array(self.imu_times)
            # imu_ts_np.sort()  # Sort timestamps

            x_imu_lines = []
            y_imu_lines = []
            for ts in imu_ts_np:
                x_imu_lines.extend(
                    [ts.astype("datetime64[s]"), ts.astype("datetime64[s]"), None]
                )  # X coordinates for vertical line
                y_imu_lines.extend([0, 1, None])  # Y coordinates (0 to 1)

            fig.add_trace(
                go.Scatter(
                    x=x_imu_lines,
                    y=y_imu_lines,
                    mode="lines",
                    name="IMU Data",
                    line=dict(color="green"),
                    hovertemplate="Timestamp: %{x|%Y-%m-%d %H:%M:%S.%L}<extra></extra>",
                ),
                row=2,
                col=1,
            )
            fig.update_yaxes(title_text="Existence", range=[0, 1.1], row=2, col=1)
        else:
            self.get_logger().info("No IMU timestamp data to plot for timestamp visualization.")

        fig.update_layout(
            title={
                "text": "Image and IMU Timestamp Visualization",
                "x": 0.5,
                "xanchor": "center",
            },
        )
        # Set shared X-axis title
        # fig.update_xaxes(title_text="Timestamp (s)", row=2, col=1)

        plot(fig, filename="timestamp_visualization_plot.html", auto_open=False)
        pio.write_image(fig, "timestamp_visualization_plot.png")
        self.get_logger().info("Timestamp visualization plot saved as timestamp_visualization_plot.html/.png")


def main(args=None):
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Generate timestamp visualization plots for VIO verification by analyzing image and IMU data"
    )
    parser.add_argument("--image-topic", type=str, help="Image topic to subscribe to")
    parser.add_argument("--imu-topic", type=str, help="IMU topic to subscribe to")

    parsed_args, remaining_args = parser.parse_known_args(args)

    rclpy.init(args=remaining_args)

    node = FlowIMUPlotNode(image_topic=parsed_args.image_topic, imu_topic=parsed_args.imu_topic, use_ros_time=True)

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info("Shutting down via KeyboardInterrupt")
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
