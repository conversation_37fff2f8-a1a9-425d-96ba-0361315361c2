# Paths to project directories
granite_ros_path: "GRANITE_ROS_PATH/deployment"
gnss_sync_path: "GNSS_SYNC_PATH/deployment"
vio_config_path: "GRANITE_ROS_PATH/deployment/data/params/default_mono.json"

# Experiment configuration
iterations_per_config: ITERATIONS_PER_CONFIG_INTEGER
output_base_path: "OUTPUT_BASE_PATH"

# Parameters to optimize
optimization_params:
  param_name: "OPTIMIZATION_PARAM_NAME_HEADING_WITH_value0.config"  # Path to parameter in VIO config file value0.config.vio_max_kfs
  start: START_VALUE_INTEGER
  end: END_VALUE_INTEGER
  step: STEP_VALUE_INTEGER

# Evaluation settings
evaluation_script_path: "EVALUATION_SCRIPT_PATH/odometry/gps_evaluation_bag.py"
customer_config: "EVALUATION_SCRIPT_PATH/deployment/data/params/CUSTOMER_CONFIG_FINE.yaml"

# ROS2 settings
monitor_topic: "MONITOR_TOPIC"
input_bag_path: "INPUT_BAG_PATH"
gnss_cutoff_time: GNSS_CUTOFF_TIME_INTEGER  # -1 to disable

recorded_topics:
  - "/ros_ap_forwarder/gps"
  - "/gnss_sync/out/gps"
  - "/gnss_sync/out/status/enabled"
  - "/gnss_sync/out/status/disabled"
  - "/gnss_sync/out/status/start"
  - "/gnss_sync/out/status/tracking"
  - "/gnss_sync/out/status/tracking_lost"
  - "/gnss_sync/out/pwc"
  - "/gnss_sync/out/relative_pwc"
  - "/gnss_sync/out/scale"
  - "/vio/path"
  - "/vio/absolute_pwc"
  - "/vio/relative_pwc"
  - "/vio/processing_time"
  - "/tf"
