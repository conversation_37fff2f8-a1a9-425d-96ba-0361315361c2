#!/usr/bin/env python3
"""
GPS Utilities
Helper functions for GPS data analysis and bounds calculation.
"""

from typing import Dict, Optional, Tuple

import numpy as np
import pandas as pd


def find_gps_bounds_simple(df: pd.DataFrame) -> Dict:
    """
    Find the most southwest and northeast positions from GPS DataFrame.

    Args:
        df: DataFrame with 'latitude' and 'longitude' columns

    Returns:
        Dictionary with southwest and northeast coordinates
    """
    if "latitude" not in df.columns or "longitude" not in df.columns:
        raise ValueError("DataFrame must contain 'latitude' and 'longitude' columns")

    min_lat = df["latitude"].min()
    max_lat = df["latitude"].max()
    min_lon = df["longitude"].min()
    max_lon = df["longitude"].max()

    return {
        "southwest": {"latitude": min_lat, "longitude": min_lon},
        "northeast": {"latitude": max_lat, "longitude": max_lon},
        "bounds": {"min_lat": min_lat, "max_lat": max_lat, "min_lon": min_lon, "max_lon": max_lon},
    }


def get_bounding_box_coordinates(df: pd.DataFrame) -> <PERSON><PERSON>[float, float, float, float]:
    """
    Get the bounding box coordinates as (min_lat, min_lon, max_lat, max_lon).

    Args:
        df: DataFrame with 'latitude' and 'longitude' columns

    Returns:
        Tuple of (min_latitude, min_longitude, max_latitude, max_longitude)
    """
    bounds = find_gps_bounds_simple(df)
    return (
        bounds["bounds"]["min_lat"],
        bounds["bounds"]["min_lon"],
        bounds["bounds"]["max_lat"],
        bounds["bounds"]["max_lon"],
    )


def calculate_distance_km(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    Calculate the approximate distance between two GPS coordinates in kilometers.
    Uses the Haversine formula for better accuracy.

    Args:
        lat1, lon1: First coordinate
        lat2, lon2: Second coordinate

    Returns:
        Distance in kilometers
    """
    # Convert to radians
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2) ** 2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2) ** 2
    c = 2 * np.arcsin(np.sqrt(a))

    # Earth's radius in kilometers
    r = 6371
    return c * r


def get_trajectory_extent_km(df: pd.DataFrame) -> Dict[str, float]:
    """
    Calculate the extent of the GPS trajectory in kilometers.

    Args:
        df: DataFrame with 'latitude' and 'longitude' columns

    Returns:
        Dictionary with lat_span_km and lon_span_km
    """
    bounds = find_gps_bounds_simple(df)

    # Calculate spans in degrees
    lat_span_deg = bounds["bounds"]["max_lat"] - bounds["bounds"]["min_lat"]
    lon_span_deg = bounds["bounds"]["max_lon"] - bounds["bounds"]["min_lon"]

    # Convert to kilometers (approximate)
    avg_lat = (bounds["bounds"]["min_lat"] + bounds["bounds"]["max_lat"]) / 2
    lat_span_km = lat_span_deg * 111  # 1 degree latitude ≈ 111 km
    lon_span_km = lon_span_deg * 111 * np.cos(np.radians(avg_lat))  # 1 degree longitude ≈ 111 * cos(lat) km

    return {
        "lat_span_km": lat_span_km,
        "lon_span_km": lon_span_km,
        "lat_span_deg": lat_span_deg,
        "lon_span_deg": lon_span_deg,
    }


def find_extreme_positions(df: pd.DataFrame) -> Dict:
    """
    Find the actual GPS points that represent the extreme positions.

    Args:
        df: DataFrame with 'latitude', 'longitude', and optionally 'timestamp' columns

    Returns:
        Dictionary with southwest and northeast point details
    """
    bounds = find_gps_bounds_simple(df)

    # Find the actual points at the extreme positions
    southwest_point = (
        df[
            (df["latitude"] == bounds["southwest"]["latitude"]) & (df["longitude"] == bounds["southwest"]["longitude"])
        ].iloc[0]
        if len(
            df[
                (df["latitude"] == bounds["southwest"]["latitude"])
                & (df["longitude"] == bounds["southwest"]["longitude"])
            ]
        )
        > 0
        else None
    )

    northeast_point = (
        df[
            (df["latitude"] == bounds["northeast"]["latitude"]) & (df["longitude"] == bounds["northeast"]["longitude"])
        ].iloc[0]
        if len(
            df[
                (df["latitude"] == bounds["northeast"]["latitude"])
                & (df["longitude"] == bounds["northeast"]["longitude"])
            ]
        )
        > 0
        else None
    )

    return {"southwest_point": southwest_point, "northeast_point": northeast_point, "bounds": bounds}


# Example usage function
def analyze_gps_file(csv_path: str) -> Dict:
    """
    Analyze a GPS CSV file and return comprehensive bounds information.

    Args:
        csv_path: Path to the CSV file

    Returns:
        Dictionary with complete analysis results
    """
    df = pd.read_csv(csv_path)

    bounds = find_gps_bounds_simple(df)
    extent = get_trajectory_extent_km(df)
    extreme_points = find_extreme_positions(df)

    return {
        "file_path": csv_path,
        "total_points": len(df),
        "bounds": bounds,
        "extent": extent,
        "extreme_points": extreme_points,
    }
