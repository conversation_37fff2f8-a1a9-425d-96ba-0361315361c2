#!/usr/bin/env python3
"""
Example usage of GPS utilities
"""

import pandas as pd

from gps_utils import (
    analyze_gps_file,
    calculate_distance_km,
    find_gps_bounds_simple,
    get_bounding_box_coordinates,
    get_trajectory_extent_km,
)


def example_basic_usage():
    """Example of basic GPS bounds analysis."""
    print("=== Basic GPS Bounds Analysis ===")

    # Load your GPS data
    df = pd.read_csv("input/gilching_300.csv")

    # Find the bounds
    bounds = find_gps_bounds_simple(df)

    print(f"Southwest: {bounds['southwest']['latitude']:.6f}°, {bounds['southwest']['longitude']:.6f}°")
    print(f"Northeast: {bounds['northeast']['latitude']:.6f}°, {bounds['northeast']['longitude']:.6f}°")

    # Get bounding box coordinates
    min_lat, min_lon, max_lat, max_lon = get_bounding_box_coordinates(df)
    print(f"Bounding box: ({min_lat:.6f}, {min_lon:.6f}) to ({max_lat:.6f}, {max_lon:.6f})")

    # Calculate extent in kilometers
    extent = get_trajectory_extent_km(df)
    print(f"Trajectory size: {extent['lat_span_km']:.2f} km × {extent['lon_span_km']:.2f} km")


def example_distance_calculation():
    """Example of calculating distances between GPS points."""
    print("\n=== Distance Calculation Example ===")

    df = pd.read_csv("input/gilching_300.csv")

    # Get the extreme points
    bounds = find_gps_bounds_simple(df)

    # Calculate distance between southwest and northeast
    distance = calculate_distance_km(
        bounds["southwest"]["latitude"],
        bounds["southwest"]["longitude"],
        bounds["northeast"]["latitude"],
        bounds["northeast"]["longitude"],
    )

    print(f"Distance from southwest to northeast: {distance:.2f} km")


def example_comprehensive_analysis():
    """Example of comprehensive GPS analysis."""
    print("\n=== Comprehensive Analysis ===")

    # Analyze a GPS file
    analysis = analyze_gps_file("input/gilching_300.csv")

    print(f"File: {analysis['file_path']}")
    print(f"Total points: {analysis['total_points']:,}")
    print(f"Latitude span: {analysis['extent']['lat_span_deg']:.6f}° ({analysis['extent']['lat_span_km']:.2f} km)")
    print(f"Longitude span: {analysis['extent']['lon_span_deg']:.6f}° ({analysis['extent']['lon_span_km']:.2f} km)")


def example_multiple_files():
    """Example of analyzing multiple GPS files."""
    print("\n=== Multiple Files Analysis ===")

    files = ["input/gilching_300.csv", "input/kitu_07_30.csv", "input/new_quantum_switch_ssd.csv"]

    for file_path in files:
        try:
            analysis = analyze_gps_file(file_path)
            print(f"\n{analysis['file_path']}:")
            print(f"  Points: {analysis['total_points']:,}")
            print(f"  Size: {analysis['extent']['lat_span_km']:.2f} × {analysis['extent']['lon_span_km']:.2f} km")
            print(
                f"  Southwest: {analysis['bounds']['southwest']['latitude']:.6f}°, {analysis['bounds']['southwest']['longitude']:.6f}°"
            )
            print(
                f"  Northeast: {analysis['bounds']['northeast']['latitude']:.6f}°, {analysis['bounds']['northeast']['longitude']:.6f}°"
            )
        except Exception as e:
            print(f"Error processing {file_path}: {e}")


if __name__ == "__main__":
    example_basic_usage()
    example_distance_calculation()
    example_comprehensive_analysis()
    example_multiple_files()
