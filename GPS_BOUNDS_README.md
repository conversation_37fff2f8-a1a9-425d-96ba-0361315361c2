# GPS Bounds Analysis

This repository contains tools to find the most southwest and northeast positions from GPS trajectory data.

## The Logic

### Finding Extreme Positions

**Most Southwest Position:**
- **Latitude**: Minimum latitude (most negative or closest to -90°)
- **Longitude**: Minimum longitude (most negative or closest to -180°)

**Most Northeast Position:**
- **Latitude**: Maximum latitude (most positive or closest to 90°)
- **Longitude**: Maximum longitude (most positive or closest to 180°)

### Why This Logic Works

1. **Southwest = Minimum Coordinates**: In GPS coordinates, the southwest corner of any area has the lowest latitude and longitude values.

2. **Northeast = Maximum Coordinates**: The northeast corner has the highest latitude and longitude values.

3. **Bounding Box**: These two points define a rectangular bounding box that contains all GPS points in the trajectory.

## Files Overview

### 1. `gps_bounds_analyzer.py`
Command-line tool for analyzing GPS CSV files.

**Usage:**
```bash
python3 gps_bounds_analyzer.py input/your_file.csv
python3 gps_bounds_analyzer.py input/your_file.csv --output results.json
```

**Features:**
- Finds southwest and northeast positions
- Calculates trajectory extent in kilometers
- Provides timestamp information for extreme points
- Can save results to JSON file

### 2. `gps_utils.py`
Utility functions for GPS analysis that can be imported into other scripts.

**Key Functions:**
- `find_gps_bounds_simple()`: Basic bounds calculation
- `get_bounding_box_coordinates()`: Returns bounding box as tuple
- `calculate_distance_km()`: Haversine distance calculation
- `get_trajectory_extent_km()`: Calculate trajectory size
- `analyze_gps_file()`: Comprehensive file analysis

### 3. `gps_utils_improved.py`
Enhanced version with data validation and quality checks.

**Key Features:**
- Filters out invalid GPS coordinates (0,0, NaN, out-of-range)
- Provides data quality metrics
- Robust error handling
- Detailed analysis reports

### 4. `example_usage.py`
Example scripts showing how to use the utilities.

## Data Format

The tools expect CSV files with these columns:
- `timestamp`: Unix timestamp
- `latitude`: Latitude in decimal degrees
- `longitude`: Longitude in decimal degrees
- `altitude`: Altitude in meters (optional)

## Example Results

### Gilching Dataset
```
📍 Most Southwest Position:
   Latitude:  48.12158796°
   Longitude: 11.27359446°

📍 Most Northeast Position:
   Latitude:  48.12646342°
   Longitude: 11.28484333°

📐 Bounding Box:
   Span: 0.004875° lat × 0.011249° lon
   Approximate size: 0.54 km × 0.83 km
```

## Distance Calculations

The tools use two methods for distance calculations:

1. **Simple Approximation**: 1 degree latitude ≈ 111 km, 1 degree longitude ≈ 111 × cos(latitude) km
2. **Haversine Formula**: More accurate calculation for actual distances between points

## Data Validation

The improved version (`gps_utils_improved.py`) includes validation for:
- **Zero coordinates**: (0,0) is often an invalid GPS reading
- **Out-of-range values**: Latitude must be -90 to 90, longitude -180 to 180
- **Missing values**: NaN or null coordinates
- **Data quality metrics**: Percentage of valid vs invalid points

## Usage Examples

### Basic Usage
```python
import pandas as pd
from gps_utils import find_gps_bounds_simple

df = pd.read_csv('your_gps_data.csv')
bounds = find_gps_bounds_simple(df)

print(f"Southwest: {bounds['southwest']['latitude']}, {bounds['southwest']['longitude']}")
print(f"Northeast: {bounds['northeast']['latitude']}, {bounds['northeast']['longitude']}")
```

### Robust Analysis
```python
from gps_utils_improved import analyze_gps_file_robust

analysis = analyze_gps_file_robust('your_gps_data.csv')
print(f"Valid points: {analysis['data_quality']['valid_points']}")
print(f"Trajectory size: {analysis['extent']['lat_span_km']} × {analysis['extent']['lon_span_km']} km")
```

## Command Line Usage

```bash
# Analyze a single file
python3 gps_bounds_analyzer.py input/gilching_300.csv

# Save results to JSON
python3 gps_bounds_analyzer.py input/gilching_300.csv --output results.json

# Run improved analysis
python3 gps_utils_improved.py

# Run examples
python3 example_usage.py
```

## Key Insights

1. **Coordinate System**: GPS uses a spherical coordinate system where:
   - Latitude ranges from -90° (South Pole) to +90° (North Pole)
   - Longitude ranges from -180° to +180°

2. **Extreme Positions**: The southwest and northeast positions define the corners of a rectangular bounding box that contains all GPS points.

3. **Distance Accuracy**: The Haversine formula provides more accurate distance calculations than simple degree-to-kilometer conversions.

4. **Data Quality**: Real GPS data often contains invalid readings that should be filtered out for accurate analysis.

## Applications

- **Trajectory Analysis**: Understanding the spatial extent of GPS tracks
- **Data Validation**: Identifying and filtering invalid GPS readings
- **Visualization**: Creating bounding boxes for mapping applications
- **Performance Metrics**: Calculating total distance and area covered
- **Quality Assessment**: Evaluating GPS data quality and coverage 